import { Memo, use$ } from '@legendapp/state/react'
import { authApi, global$ } from '@mass/api'
import {
  ChevronDownIcon,
  ExternalIcon,
  FileIcon,
  HelpIcon,
  HomeIcon,
  LogoFull,
  LogoutIcon,
  NotificationIcon,
  SelectorVerticalIcon,
  SettingsIcon,
  UserIcon,
} from '@mass/icons'
import { useLocation, useNavigate } from '@tanstack/react-router'
import clsx from 'clsx'
import { type FC, useCallback } from 'react'
import { useTranslation } from 'react-i18next'
import { Button, Disclosure, Link, Popover, Section, Text } from '../../../shared'

export const Navigation: FC<{
  className?: string
}> = ({ className }) => {
  const { t: common } = useTranslation('common')
  const { pathname } = useLocation()
  const navigate = useNavigate()

  const user = use$(() => global$.user.get())

  const isHelpDisclosureOpen = pathname === '/faq'

  const handleLogout = useCallback(async () => {
    await authApi.logout()
    navigate({ to: '/login' })
  }, [navigate])

  return (
    <nav
      className={clsx(
        'hidden md:block', // display
        'flex flex-col gap-12', // flex
        'h-full min-h-screen w-full min-w-[250px] max-w-[288px]', // sizing
        'border-accessory-1 border-r', // accessory
        'px-4 pt-12 pb-4', // padding & margin
        'overflow-y-auto', // scroll
        className,
      )}>
      <section className='flex items-center px-12 py-6'>
        <LogoFull />
      </section>

      <Section label={common('general')} placement='outside'>
        <Link to='/' variant='navigation' data-state={pathname === '/' ? 'active' : undefined}>
          <HomeIcon className='h-[20px] w-[20px]' /> {common('subscriptions')}
        </Link>
        <Link
          to='/notifications'
          variant='navigation'
          data-state={pathname === '/notifications' ? 'active' : undefined}>
          <NotificationIcon className='h-[20px] w-[20px]' /> {common('notifications')}
        </Link>
        <Link
          to='/complaints-requests'
          variant='navigation'
          data-state={pathname === '/complaints-requests' ? 'active' : undefined}>
          <FileIcon className='h-[20px] w-[20px]' /> {common('complaints-requests')}
        </Link>
      </Section>

      <Section label={common('settings-help')} placement='outside'>
        <Link to='/settings' variant='navigation' data-state={pathname === '/settings' ? 'active' : undefined}>
          <SettingsIcon className='h-[20px] w-[20px]' /> {common('settings')}
        </Link>

        <Disclosure
          variant='navigation'
          defaultOpen={isHelpDisclosureOpen}
          label={({ open }) => (
            <>
              <HelpIcon className='h-[20px] w-[20px]' /> {common('help-support')}
              <ChevronDownIcon
                className={clsx(
                  'ml-auto', // margin
                  'transition-transform duration-300', // animation
                  {
                    '-rotate-90': open,
                  },
                )}
              />
            </>
          )}>
          <Link to='/faq' variant='navigation-2' data-state={pathname === '/faq' ? 'active' : undefined}>
            {common('FAQ-long')}
          </Link>

          <Memo>
            {() => (
              <>
                <Link
                  to={global$.documents.pdfs.about.get() ?? '#'}
                  target='_blank'
                  variant='navigation-2'
                  className='items-center justify-between'>
                  {common('about-us')} <ExternalIcon width={16} height={16} />
                </Link>

                <Link
                  to={global$.documents.pdfs.userGuide.get() ?? '#'}
                  target='_blank'
                  variant='navigation-2'
                  className='items-center justify-between'>
                  {common('user-guide')} <ExternalIcon width={16} height={16} />
                </Link>
              </>
            )}
          </Memo>
        </Disclosure>
      </Section>

      <Popover
        className='mt-auto'
        popoverWidth='269px'
        buttonContent={({ open }) => (
          <>
            <UserIcon className='h-[25px] w-[25px]' />

            <div className='flex w-full justify-start gap-2'>
              {user?.type === 'end' ? (
                <>
                  <Text variant='dim-xs'> {user.firstName} </Text>
                  <Text variant='dim-xs'> {user.lastName} </Text>
                </>
              ) : (
                <Text variant='dim-xs'> {common('anon')} </Text>
              )}
            </div>

            <SelectorVerticalIcon
              className={clsx(
                'ml-auto', // margin
                'transition-transform duration-300', // animation
                {
                  '-rotate-90': open,
                },
              )}
            />
          </>
        )}
        popoverUnstyled>
        <Button
          variant='hover-error'
          className={clsx(
            'justify-start gap-4!', // flex
            'text-dim-1! hover:text-white!', // styling
            'rounded-c1',
          )}
          onClick={handleLogout}>
          <LogoutIcon className='h-[20px] w-[20px]' />

          {common('logout')}
        </Button>
      </Popover>
    </nav>
  )
}
